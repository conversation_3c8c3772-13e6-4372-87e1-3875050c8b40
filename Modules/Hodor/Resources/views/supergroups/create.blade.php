@extends('hodor::layouts.master')

@section('content')
<section class="content">
    <div class="container-fluid">
        <div class="card card-default">
            <div class="card-header">
                <h3 class="card-title">Supergroup Details</h3>
            </div>
            @include('hodor::common.alert')
            {!! Form::open(array('method' => 'POST', 'class' => 'main', 'route' => array('hodor.supergroups.store'))) !!}
            {!! Form::token() !!}
                <div class="card-body">
                    <div class="row">
                        <div class="col-sm-6">
                            <div class="form-group">
                                {!! Form::label('name', 'Name') !!}
                                {!! Form::text('name',null,['class' => 'form-control' . ($errors->has('name') ? ' is-invalid' : null)]) !!}
                            </div>
                        </div>
                    </div>
                </div>
                <div class="card-footer">
                    <button type="submit" class="col-md-4 btn btn-primary">Submit</button>
                </div>
            {!! Form::close() !!}
        </div>
    </div>
</section>
@endsection
