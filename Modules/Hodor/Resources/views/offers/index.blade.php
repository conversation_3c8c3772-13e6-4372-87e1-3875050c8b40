@extends('hodor::layouts.master')

@section('content')
<section class="content-header">
    <div class="container-fluid">
        <div class="row">
            <div class="col-sm-6"></div>
            <div class="col-sm-6">
                <div class="float-right">
                    <a href="{{ route('hodor.offers.create') }}" class="btn btn-primary"><i class="fas fa-plus"></i> Commercial Offer</a>
                </div>
            </div>
        </div>
    </div>
</section>

<section class="content">
    <div class="card">
        <div class="card-header">
            <h3 class="card-title">Search Filters</h3>
            <div class="card-tools">
                <button type="button" class="btn btn-tool" data-card-widget="collapse" title="Collapse">
                    <i class="fas fa-minus"></i>
                </button>
            </div>
        </div>
        <div class="card-body clearfix">
            <form method="GET" action="{{ route('hodor.offers.index') }}">
                <div class="row">
                    <div class="form-group col-md-3">
                        <label>Pickup after</label>
                        <input type="date" class="form-control custom-select" name="pickup_after" value="{{ request('pickup_after') }}">
                    </div>
                    <div class="form-group col-md-3">
                        <label>Pickup before</label>
                        <input type="date" class="form-control custom-select" name="pickup_before" value="{{ request('pickup_before') }}">
                    </div>
                    <div class="form-group col-md-3">
                        <label>Enabled</label>
                        <select class="form-control custom-select" name="enabled">
                            <option value="">All</option>
                            <option value="yes" @if(request('enabled') == 'yes') selected @endif>Yes</option>
                            <option value="no" @if(request('enabled') == 'no') selected @endif>No</option>
                        </select>
                    </div>
                    <div class="form-group col-md-3">
                        <label>Sent</label>
                        <select class="form-control custom-select" name="sent">
                            <option value="">All</option>
                            <option value="yes" @if(request('sent') == 'yes') selected @endif>Yes</option>
                            <option value="no" @if(request('sent') == 'no') selected @endif>No</option>
                        </select>
                    </div>
                </div>
                <div class="row">
                    <div class="form-group col-md-3">
                        <label>Id</label>
                        <input type="text" class="form-control" name="id" value="{{ request('id') }}"/>
                    </div>
                </div>
                <div class="row">
                    <div class="col-sm-6">
                        <button type="submit" class="btn btn-default">SEARCH</button>
                    </div>
                    <div class="col-sm-6">
                        <div class="float-sm-right">
                            <a href="{{ route('hodor.offers.index') }}">Reset</a>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
</section>

<section class="content">
    <div class="container-fluid">
        <div class="row">
            @include('hodor::common.alert')
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <div>{{ $offers->count() }} of {{ $offers->total() }} items</div>
                    </div>
                    <div class="card-body table-responsive p-0">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>Title / slug</th>
                                    <th>Pickup</th>
                                    <th>Dropoff</th>
                                    <th>Price</th>
                                    <th>Customer</th>
                                    <th>Enabled</th>
                                    <th>Ready</th>
                                    <th>Sent</th>
                                    <th>Reservations</th>
                                    <th></th>
                                </tr>
                            </thead>
                            <tbody>
                            @foreach ($offers as $offer)
                                <tr>
                                    <td>
                                        <strong><a href="{{ route('hodor.offers.edit', $offer->id) }}">{{ $offer->id }}</a></strong>
                                    </td>
                                    <td>
                                        <strong><a href="{{ route('hodor.offers.edit', $offer->id) }}">{{ $offer->title }}</a></strong>
                                        <br>
                                        {{ $offer->slug }}
{{--                                        <br>--}}
{{--                                        <br>--}}
{{--                                        {!! Form::open(array('method' => 'PUT', 'class' => '', 'route' => array('hodor.offers.slugify', $offer->id))) !!}--}}
{{--                                        {!! Form::token() !!}--}}
{{--                                        <button class="btn btn-outline-warning" title="Slugify" onclick="return confirm('Are you sure you want to slugify this item?');"><i class="fas fa-globe"></i></button>--}}
{{--                                        {!! Form::close() !!}--}}
                                    </td>
                                    <td>
                                        <b>{{ substr($offer->pickup_date, 0, 10) }}</b>
                                        <br>
                                        {!! substr($offer->pickup_time, 0, 5) !!}
                                        <br>
                                        @if($offer->pickup_loc()->exists()){{ $offer->pickup_loc->name }}@endif
                                    </td>
                                    <td>
                                        <b>{!! substr($offer->dropoff_date, 0, 10) !!}</b>
                                        <br>
                                        {!! substr($offer->dropoff_time, 0, 5) !!}
                                        <br>
                                        @if($offer->dropoff_loc()->exists()){{ $offer->dropoff_loc->name }}@endif
                                    </td>
                                    <td>@if($offer->offered_price > 0)€{!! $offer->offered_price !!}@endif</td>
                                    <td>@if($offer->customer()->exists()){!! $offer->customer->name !!} {{ $offer->customer->email }}@endif</td>
                                    <td>
                                        @if($offer->enabled)
                                            <span class="badge bg-success">YES</span>
                                        @else
                                            <span class="badge bg-danger">NO</span>
                                        @endif
                                    </td>
                                    <td>
                                        @if($offer->isReady())
                                            <span class="badge bg-success">YES</span>
                                            <br>
                                            <br>
                                            <p>
                                                <a target="_blank" class="btn btn-outline-info" title="Edit" href="{{ route('offers.show', $offer->slug) }}">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                            </p>
                                            {!! Form::open(array('method' => 'PUT', 'class' => '', 'route' => array('hodor.offers.notify', $offer->id))) !!}
                                            {!! Form::token() !!}
                                            <button class="btn btn-outline-warning" title="Notify" onclick="return confirm('Are you sure you want to send this offer to the customer?');"><i class="fas fa-envelope"></i></button>
                                            {!! Form::close() !!}
                                        @else
                                            <span class="badge bg-danger">NO</span>
                                        @endif
                                    </td>
                                    <td>
                                        @if($offer->sent)
                                            <span class="badge bg-success">YES</span>
                                            <br>
                                            @if( !empty($offer->sent_at) ){{ $offer->sent_at->format('Y-m-d H:i') }}@endif
                                        @else
                                            <span class="badge bg-danger">NO</span>
                                        @endif
                                    </td>
                                    <td>
                                        @if($offer->reservations->count() > 0)
                                            <a target="_blank" href="{{ route('hodor.reservations.index', ['offer_id' => $offer->id]) }}">
                                                {{ $offer->reservations->count() }}
                                            </a>
                                        @else
                                            -
                                        @endif
                                    </td>
                                    <td class="text-right project-actions">
                                        <p>
                                            <a class="btn btn-outline-info" title="Edit" href="{{ route('hodor.offers.edit', $offer->id) }}">
                                                <i class="fas fa-pen"></i>
                                            </a>
                                        </p>
{{--                                        {!! Form::open(array('method' => 'DELETE', 'class' => '', 'route' => array('hodor.offers.destroy', $offer->id))) !!}--}}
{{--                                            {!! Form::token() !!}--}}
{{--                                            <button class="btn btn-outline-danger" title="Delete" onclick="return confirm('Are you sure you want to delete this item?');"><i class="fas fa-trash"></i></button>--}}
{{--                                        {!! Form::close() !!}--}}
                                    </td>
                                </tr>
                            @endforeach
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div>{{ $offers->links() }}</div>
</section>
@endsection
