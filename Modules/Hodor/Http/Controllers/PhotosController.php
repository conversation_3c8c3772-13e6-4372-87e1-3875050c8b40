<?php

namespace Modules\Hodor\Http\Controllers;

use App\Photo;
use Illuminate\Contracts\Support\Renderable;
use Illuminate\Http\Request;
use Modules\Hodor\Http\Requests\PhotoUpdateRequest;

class PhotosController extends HodorController
{
    /**
     * @var $languages
     */
    private $languages;

    public function __construct()
    {
        // languages
        foreach (config('laravellocalization.supportedLocales') as $langKey => $langData){
            $this->languages[$langKey] = $langData['name'];
        }
    }

    /**
     * Display a listing of the resource.
     * @return Renderable
     */
    public function index(Request $request)
    {
        $this->view_data['photos']      = Photo::orderBy('id', 'desc')->paginate(20);
        $this->view_data['page_title']  = 'Photos';
        $this->view_data['languages']   = $this->languages;

        return view('hodor::photos.index', $this->view_data);
    }

    /**
     * Show the form for editing the specified resource.
     * @param int $id
     * @return Renderable
     */
    public function edit(int $id)
    {
        $photo = Photo::findOrFail($id);

        $this->view_data['photo']       = $photo;
        $this->view_data['page_title']  = 'Editing photo: ' . $photo->filename;

        // languages
        $this->view_data['languages'] = $this->languages;

        return view('hodor::photos.edit', $this->view_data);
    }

    /**
     * Update the specified resource in storage.
     * @param PhotoUpdateRequest $request
     * @param int $id
     * @return Renderable
     */
    public function update(PhotoUpdateRequest $request, $id)
    {
        $photo = Photo::findOrFail($id);

        $photo->update($request->validated());

        // hack to bypass the issue of updating the updated_at filed of the post on translation change
        $photo->touch();

        return redirect()->route('hodor.photos.edit',  $photo->id)
            ->withSuccess('Entity with id: ' . $photo->id . ' was successfully updated!');
    }

    /**
     * Make the photo main (of all the photos of the parent_.
     * @param int $id
     */
    public function makeMain($id)
    {
        $photo      = Photo::findOrFail($id);

        // grab the parent model from the photo
        // the "model" relationship belongs to the Spatie/Media class
        $photoable  = $photo->model;

        // hack to bypass the issue of updating the updated_at filed of the post on translation change
        $photoable->touch();

        // grab all related photos
        $all_photos = $photoable->getMedia('photos');

        // and make them not main
        foreach ($all_photos as $photo_item)
        {
            $photo_item->main = false;

            // hack to get the main photo via spatie's getFirstMedia mefod
            // manipulate the ordering of media
            $photo_item->order_column = $photo_item->id;

            $photo_item->save();
        }

        // make the specified photo main
        $photo->main = true;

        // so as to be returned in the gtfirst media mefod
        $photo->order_column = 0;
        $photo->save();

        return back()->withSuccess('Entity with id: ' . $photoable->id . ' was successfully updated!');
    }

    /**
     * Remove the specified resource from storage.
     * @param int $id
     */
    public function destroy($id)
    {
        $deleteItem = Photo::find($id);

        try
        {
            $delete_result = $deleteItem->delete();

            if ($delete_result)
            {
                return back()->withSuccess('Entity with id: ' . $id . ' was successfully deleted!');
            }
            else
            {
                return back()->withErrors(['msg' => 'An error occured!']);
            }
        } catch(\Exception $e)
        {
            return back()->withErrors(['msg' => $e->getMessage()]);
        }
    }
}