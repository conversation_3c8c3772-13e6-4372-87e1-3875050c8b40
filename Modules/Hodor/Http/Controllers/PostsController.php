<?php

namespace Modules\Hodor\Http\Controllers;

use App\Motif;
use App\Post;
use App\Services\Search\PostSearch;
use App\Tag;
use Illuminate\Contracts\Support\Renderable;
use Illuminate\Http\Request;
use Modules\Hodor\Http\Requests\PostPhotoStoreRequest;
use Modules\Hodor\Http\Requests\PostStoreRequest;
use Modules\Hodor\Http\Requests\PostUpdateRequest;
use Modules\Hodor\Http\Requests\PostSearchRequest;

class PostsController extends HodorController
{
    /**
     * @var $languages
     */
    private $languages;

    public function __construct()
    {
        // languages
        foreach (config('laravellocalization.supportedLocales') as $langKey => $langData){
            $this->languages[$langKey] = $langData['name'];
        }
    }

    /**
     * Display a listing of the resource.
     * @return Renderable
     */
    public function index(PostSearchRequest $request)
    {
        $this->view_data['posts']       = PostSearch::apply($request);
        $this->view_data['page_title']  = 'Blog Posts';
        $this->view_data['languages']   = $this->languages;
        $this->view_data['motifs']      = Motif::orderBy('title', 'asc')->pluck('title', 'id')->all();

        return view('hodor::posts.index', $this->view_data);
    }

    /**
     * Show the form for creating a new resource.
     * @return Renderable
     */
    public function create()
    {
        $this->view_data['page_title']  = 'Create New Blog Post';
        $this->view_data['languages'] = $this->languages;

        return view('hodor::posts.create', $this->view_data);
    }

    /**
     * Store a newly created resource in storage.
     * @param Request $request
     * @return Renderable
     */
    public function store(PostStoreRequest $request)
    {
        $post = Post::create($request->validated());

        return redirect()->route('hodor.posts.edit',  $post->id)
            ->withSuccess('Entity with id: ' . $post->id . ' was successfully saved!');
    }

    /**
     * Show the form for editing the specified resource.
     * @param int $id
     * @return Renderable
     */
    public function edit(int $id)
    {
        $post = Post::findOrFail($id);

        $this->view_data['post']      = $post;
        $this->view_data['page_title']  = $post->title;

        // languages
        $this->view_data['languages'] = $this->languages;
        // tags
        $this->view_data['tags'] = Tag::orderBy('title', 'asc')->pluck('title', 'id');
        // motifs
        $this->view_data['motifs'] = Motif::orderBy('title', 'asc')->pluck('title', 'id');

        return view('hodor::posts.edit', $this->view_data);
    }

    /**
     * Update the specified resource in storage.
     * @param PostUpdateRequest $request
     * @param int $id
     * @return Renderable
     */
    public function update(PostUpdateRequest $request, $id)
    {
        $post = Post::findOrFail($id);

        $post->update($request->validated());

        // hack to bypass the issue of updating the updated_at filed of the post on translation change
        $post->touch();

        // sync tags
        $post->tags()->sync($request->input('tags'));

        // sync motifs
        $post->motifs()->sync($request->input('motifs'));

        return redirect()->route('hodor.posts.edit',  $post->id)
            ->withSuccess('Entity with id: ' . $post->id . ' was successfully updated!');
    }

    /**
     * Remove the specified resource from storage.
     * @param int $id
     * @return Renderable
     */
    public function destroy($id)
    {
        $deleteItem =  Post::find($id);

        if ($deleteItem->delete()) {
            return redirect()->route('hodor.posts.index')
                ->withSuccess('Entity with id: ' . $id . ' was successfully deleted!');
        } else {
            return redirect()->route('hodor.posts.index')
                ->withError('msg', 'An error occured!');
        }
    }

    /**
     * Show the form for previewing the photos of the specified resource.
     * @param int $id
     * @return Renderable
     */
    public function photosIndex(int $post_id)
    {
        $post = Post::findOrFail($post_id);

        $this->view_data['post']        = $post;
        $this->view_data['photos']      = $post->getMedia('photos');
        $this->view_data['page_title']  = $post->title . ': Photo Management';

        // languages
        $this->view_data['languages'] = $this->languages;

        $this->view_data['upload_max_filesize'] = $this->return_bytes(ini_get('upload_max_filesize'));
        $this->view_data['upload_max_filesize_short'] = ini_get('upload_max_filesize');

        return view('hodor::posts.index_photos', $this->view_data);
    }


    /**
     * Update the specified resource in storage.
     * @param PostPhotoStoreRequest $request
     * @param int $id
     * @return Renderable
     */
    public function photoStore(PostPhotoStoreRequest $request, $post_id)
    {
        $post = Post::findOrFail($post_id);

        $post->update($request->validated());

        // hack to bypass the issue of updating the updated_at filed of the post on translation change
        $post->touch();

        // handle image data
        if ($request->hasFile('photo') && $request->file('photo')->isValid())
        {
            $post->storePhoto('img/posts/');
        }

        return redirect()->route('hodor.posts.photos.index',  $post->id)
            ->withSuccess('Entity with id: ' . $post->id . ' was successfully updated!');
    }


    /**
     * Convert byte value from shorthand byte notation
     * @param string $val
     */
    private function return_bytes($val) {
        $val = trim($val);
        $last = strtolower($val[strlen($val)-1]);
        $val = (int)$val;
        switch($last) {
            // The 'G' modifier is available since PHP 5.1.0
            case 'g':
                $val *= 1024;
            case 'm':
                $val *= 1024;
            case 'k':
                $val *= 1024;
        }

        return $val;
    }
}
