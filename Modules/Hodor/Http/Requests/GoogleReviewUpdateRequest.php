<?php

namespace Modules\Hodor\Http\Requests;

use App\Http\Requests\Request;

class GoogleReviewUpdateRequest extends Request
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'reservation_id'    => 'nullable|integer|exists:reservations,id',
            'location_id'       => 'nullable|integer|exists:locations,id',
            'translated_text'   => 'required',
            'author_shown_name' => 'required',
            'published'         => '',
            'testimonialised'   => '',
        ];
    }
}
