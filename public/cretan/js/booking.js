/**
 * Created by k<PERSON><PERSON> on 5/2/2017.
 */

function getParameterByName(name, url) {
    if (!url) {
        url = window.location.href;
    }
    name = name.replace(/[\[\]]/g, "\\$&");
    var regex = new RegExp("[?&]" + name + "(=([^&#]*)|&|#|$)"),
        results = regex.exec(url);
    if (!results) return null;
    if (!results[2]) return '';
    return decodeURIComponent(results[2].replace(/\+/g, " "));
}

$( document ).ready(function() {
    $.LoadingOverlaySetup({
        color           : "rgba(0, 0, 0, 0.4)",
        image       : "",
        fontawesome : "fa fa-spin fa-cog text-white",
        fade: 0
    });

    if($('#quote-modal').length){
        $('body').append($('#quote-modal'));
    }

    //=== ajax calls to steps (3,4,5) ===//
    $('body').on('click', 'a.ajaxStep', function(e){
        e.preventDefault();
        var step = $(this).data('step'); // the target step
        if(isNaN(step)) return false;

        // get the group selected if call is for step 3 (from step 2)
        var selectedGroup = false;
        if($(this).hasClass('select-group')) {
            var selectedGroup = $(this).data('groupid');
        }
        var form   = $('form#bookingDataForm');
        if($(this).hasClass('select-group')){
            // add the groupId to the hidden form
            form.find('input[name="group"]').val($(this).data('groupid'));
        }
        form.find('input[name="step"]').val(step);
        if($('#discount_coupon_tmp').length){
            form.find('input[name="discount_coupon"]').val($('#discount_coupon_tmp').val());
        }

        var method = form.find('input[name="_method"]').val() || 'POST';

        $.LoadingOverlay("show");
        $.ajax({
            type: method,
            url: form.attr('action'),
            headers: {
                "x-csrf-token": $('input[name="_token"]').val()
            },
            data: form.serialize(),
            dataType: 'json'
        })
            .fail(function() {
                $.LoadingOverlay("hide");
                swal( $('#generic-error').data("title"), $('#generic-error').data("text"), 'error' );
            })
            .done(function(data){

                if(data.status == true){
                    $('#'+data.target_id).fadeOut('normal', function(){
                        //replace the content
                        $('#'+data.target_id).html(data.contents).fadeIn('normal', function(){
                            // animate to top
                            $('html, body').animate({
                                scrollTop: '0px'
                            }, 400, function(){
                                // re-bind any selectpickers
                                if ($().selectpicker) {
                                    $('.selectpicker').selectpicker();
                                }
                                //hide loader
                                $.LoadingOverlay("hide");
                            });
                        });
                    });

                    // replace step url param with the current one
                    var currentStep = getParameterByName('step');
                    var newUrl      = location.href.replace("step="+currentStep, "step="+step);
                    // same for group if step 2->3
                    if( selectedGroup ){
                        var currentGroup = getParameterByName('group');
                        newUrl       = newUrl.replace("group="+currentGroup, "group="+selectedGroup);
                    }
                    history.pushState({}, null, newUrl);

                    // re-init sticky sidebar
                    if ($(window).width() > 992) {
                        $('.sticky-sidebar').sticky({
                            topSpacing:70,
                            zIndex:99999,
                            bottomSpacing:400,
                            responsiveWidth:true
                        });
                    }
                    $('body').removeClass('stop-scrolling');
                    $('#policy_modal').remove();
                }
                else{ // sth went wrong, probably invalid data
                    // redirect to given url if set
                    if(typeof data.redirect !== 'undefined'){
                        window.location = data.redirect;
                    }
                    // show errors/msg
                    $.LoadingOverlay("hide");
                    if(typeof data.message_popup !== 'undefined')
                    {
                        swal(data.message_popup.title,data.message_popup.text,'warning');
                    }
                    if(typeof data.errors !== 'undefined'){
                        $("[id^='error_']" ).html(" ");
                        $('.errorable').removeClass('witherror');

                        $.each(data.errors,function(index, value){
                            $('#'+index).addClass('witherror');
                            $('#error_'+index).html('<i class="fa fa-error"></i> '+value);
                        });
                    }
                }
            });

    });

    //=== accessories selection ===//
    $('body').on('click','[data-changeable]', function(e){
        e.preventDefault();
        // grab action
        var action = $(this).data('action'); // add||subtract

        var element_id      = $(this).data('element'); // the hidden input for that accessory
        var element_counter = parseInt($('#' + element_id).val()); // current element counter
        var new_count       = element_counter;

        // manipulate the counter for the appropriate accessory
        if (action == 'add')
        {
            new_count = element_counter + 1;
            $('#' + element_id).val(new_count); // update the data value
            $(this).closest('.accessory').addClass('picked');
        }
        else if (action == 'subtract')
        {
            // remove the markup for selected accessories if newCount = 0
            if(element_counter == 1) {
                $(this).closest('.accessory').removeClass('picked');
                new_count = element_counter - 1;
                $('#' + element_id).val(new_count);
            }
            // update the accessory counter
            if(element_counter >= 1) { // subtract one
                new_count = element_counter - 1;
                $('#' + element_id).val(new_count); //update the data value
            }
        }

        // get the new prices
        var url = $('#priceable_url').data('url');
        var form = $('form#bookingDataForm');
        var mini_loader = $('#mini_loader_' + element_id);
        mini_loader.show();
        $.ajax({
            type: 'POST',
            url: url,
            headers: {
                "x-csrf-token": $('input[name="_token"]').val()
            },
            data: form.serialize(),
            dataType: 'json'
        })
            .fail(function() {
                mini_loader.hide();
                // swal({ title: $('#generic-error').data( "title"), text: $('#generic-error').data("text"),   type:"error"   });
            })
            .done(function(data){
                mini_loader.hide();
                if(data.status == 1)
                {
                    updatePrices(data);
                }
                else
                {
                    // swal({ title: $('#generic-error').data( "title"), text: $('#generic-error').data("text"),   type:"error"   });
                }
            });
    });

    //=== fuel plan radio buttons ===//
    $('body').on('change', '#fuel_plan_select_0, #fuel_plan_select_1', function(e){

        $('input#fuel_plan_value').val($(this).val());
        // get the new prices
        var url = $('#priceable_url').data('url');
        var form   = $('form#bookingDataForm');
        $.ajax({
            type: 'POST',
            url: url,
            headers: {
                "x-csrf-token": $('input[name="_token"]').val()
            },
            data: form.serialize(),
            dataType: 'json'
        })
            .fail(function() {
                // swal({ title: $('#generic-error').data( "title"), text: $('#generic-error').data("text"),   type:"error"   });
            })
            .done(function(data){
                if(data.status == 1)
                {
                    updatePrices(data);
                }
                else
                {
                    // swal({ title: $('#generic-error').data( "title"), text: $('#generic-error').data("text"),   type:"error"   });
                }
            });
    });

    //=== discount coupon ===//
    $('body').on('blur', '#discount_coupon_tmp', function() {
        var form   = $('form#bookingDataForm');
        form.find('input[name="discount_coupon"]').val($(this).val());

        var url         = $(this).data('url');
        $.ajax({
            type: 'POST',
            url: url,
            headers: {
                "x-csrf-token": $('input[name="_token"]').val()
            },
            data: form.serialize(),
            dataType: 'json'
        })
            .fail(function() {
                // swal({ title: $('#generic-error').data( "title"), text: $('#generic-error').data("text"),   type:"error"   });
            })
            .done(function(data){
                if(data.msg === ''){
                    $('#discount_coupon_notify').fadeOut().html('');
                }
                else {
                    $('#discount_coupon_notify').html(data.msg).fadeIn();
                }
                updatePrices(data.offer);
            });


    })
    //=== repeating client ===//
    $('body').on('click', '#repeating_client', function(e){
        // // apply modal breaking css fix (interferes with othre relative positioned elements)
        // $(document.body).append( $('#repeating_modal').detach() );
        swal({
                title: '',
                text: $('#repeatingClient_url').data( "prompt-text"),
                type: "input",
                showCancelButton: true,
                confirmButtonText: $('#repeatingClient_url').data( "prompt-submit"),
                confirmButtonColor: '#1cc7d0',
                cancelButtonText : $('#repeatingClient_url').data( "prompt-close"),
                closeOnConfirm: false,
                animation: false,
                customClass : 'repeating_client_popup',
                allowOutsideClick : true
            },
            function(inputValue){
                if (inputValue === false) return false;

                if (inputValue === "") {
                    swal.showInputError($('#repeatingClient_url').data( "warning-message"));
                    return false
                }

                // grab the booking form
                var form   = $('form#bookingDataForm');
                // grab the url
                var url = $('#repeatingClient_url').val();

                $("#repeating_client_email", form).remove();
                $('<input>').attr({
                    type: 'hidden',
                    id: 'repeating_client_email',
                    name: 'email',
                    value: inputValue
                }).appendTo(form);

                $.ajax({
                    type: 'POST',
                    url: url,
                    data: form.serialize(),
                    dataType: 'json'
                })
                    .fail(function() {
                        swal({ title: $('#repeatingClient_url').data( "error-title"), text: $('#repeatingClient_url').data( "error-message"),   type:"error"   });
                    })
                    .done(function(data){
                        if(data.status == 'success')
                        {
                             swal({   title: $('#repeatingClient_url').data( "success-title"), text: data.msg,   type:"success"  });
                        }
                        else
                        {
                            if(data.msg && data.msg.email){
                                swal.showInputError(data.msg.email);
                            }
                            else{
                                swal.showInputError($('#repeatingClient_url').data( "error-message"));
                            }
                            return false
                        }
                    });
        });
    });

    //=== show quote avalability modal ===//
    $('.quote-btn').on('click', function(){
        $('#quote_submit').data('groupid',$(this).data('groupid'));
        $('#quote-modal').modal('show');
    });

    //=== Submit quote ===//
    $('body').on('click', '#quote_submit', function(e){
        e.preventDefault();
        // show loader
        var loader = $('#ajax-loader');
        loader.show();
        // get hidden form data
        var form   = $('form#bookingDataForm');
        // replace current group with the specific unavailable
        var unavailable_group_id = $(this).data('groupid');
        var form_data            = form.serializeArray();
        $.each(form_data, function (i, field) {
            if (field.name == 'group') {
                field.value = unavailable_group_id;
            }
        });
        // add name, email
        form_data.push({
            name:'customer_name', value:$('input[name=customer_name]').val(),
        });
        form_data.push({
            name:'customer_email', value:$('input[name=customer_email]').val(),
        });

        $.ajax({
            type: 'POST',
            url: $(this).data('url'),
            data: form_data,
            dataType: 'json'
        })
            .fail(function() {
                swal({ title: $('#generic-error').data( "title"), text: $('#generic-error').data("text"),   type:"error"   });
            })
            .done(function(data){
                loader.hide();
                if(data.status == 'success')
                {
                    $('#quote-modal').modal('hide');
                    swal({   title: '', text: data.msg,   type:"success"  });
                }
                else
                {
                    if(typeof data.msg !== 'undefined'){
                        $("[id^='error_']" ).html(" ");
                        $('.errorable').removeClass('witherror');

                        $.each(data.msg,function(index, value){
                            $('#'+index).addClass('witherror');
                            $('#error_'+index).html('<i class="fa fa-error"></i> '+value);
                        });
                    }
                    else{
                        $('#quote-modal').modal('hide');
                        swal({ title: $('#generic-error').data( "title"), text: $('#generic-error').data("text"),   type:"error"   });
                    }
                }
            });
    });

    //=== COMPLETE RESERVATION ===//
    $('body').on('click', '#complete_booking', function(){
        swal({
            title: $('#processing').data( "proccessing-title"),
            text: $('#processing').data( "proccessing-message"),
            showConfirmButton:false,
            imageUrl: "../../images/ring-alt.gif",
            imageSize : '50x50',
        });

        var form   = $('form#bookingDataForm');
        var url    = $(this).data('url');

        $.ajax({
            type: 'POST',
            url: url,
            headers: {
                "x-csrf-token": $('input[name="_token"]').val()
            },
            data: form.serialize(),
            dataType: 'json'
        })
            .fail(function() {
                swal({ title: $('#generic-error').data( "title"), text: $('#generic-error').data("text"),   type:"error"   });
            })
            .done(function(data){
                if(data.status === 'success'){
                    swal.close();
                    $('body').removeClass('stop-scrolling');
                    $('#reservation-success-text').html(data.notify_text);
                    $('#success-msg-holder').show().resize();
                    $('#link-to-homepage').show();
                    $('.page-header h1').html(data.heading_text);
                    $('.pagination').remove();
                    $('.page-header h2').hide();
                    var newUrl = window.location.href.split("?")[0];
                    window.history.pushState("", "",newUrl);
                    $('html, body').animate({
                        scrollTop: '0px'
                    }, 600);
                }
                else if(data.status === 'error'){
                    if(data.msg == ''){
                        // show a generic error msg
                        swal({ title: $('#generic-error').data( "title"), text: $('#generic-error').data("text"),   type:"error"   });
                    }
                    else{
                        swal('', data.msg, 'warning');
                    }
                }
            });

    });

    //=== policy modal/link handling===//
    if($('a.policy_modal').length){
        if ($(window).width() > 768) {
            $('a.policy_modal').addClass('active-modal');
        }
    }
    $('#step2').on('click', 'a.policy_modal.active-modal', function(e){
        e.preventDefault();
        $('#policy_modal').modal('show');
    });
    $('#policy_modal').on('show.bs.modal', function() {
        $(this).show();
        setModalMaxHeight(this);
    });
    $('#policy_modal').on('hide.bs.modal', function() {
        $('#policy_modal .modal-body').scrollTop(0);
    });


    // sticky sidebar
    if ($(window).width() > 992) {
        $('.sticky-sidebar').sticky({
            topSpacing:70,
            zIndex:99999,
            bottomSpacing:400,
            responsiveWidth:true
        });
    }

});

jQuery(window).resize(function(){
    if ($(window).width() > 992) {
        $('.sticky-sidebar').sticky({
            topSpacing:70,
            zIndex:99999,
            bottomSpacing:400,
            responsiveWidth:true
        });
        $('.sticky-sidebar').sticky('update');

    }
    else{
         $('.sticky-sidebar').unstick();
    }

    if ($(window).width() > 768) {
        $('a.policy_modal').each(function(){
            $(this).addClass('active-modal');
        })
    }
    else{
        $('a.policy_modal').each(function(){
            $(this).removeClass('active-modal');
        })
    }
});

function updatePrices(data) {
    $('#initial_price').html(data['total_price_initial']);

    if (data['fuel_plan_charge'] > 0) {
        $('#fuel_plan').html(data['fuel_plan_charge']);
        $('#holder_fuel_plan').fadeIn();
    }
    else {
        $('#holder_fuel_plan').fadeOut();
    }
    $('#final_price').html(data['final_price']);

    // update accessories
    for (var key in data['accessories']) {

        var value = data['accessories'][key];

        if(value['count'] > 0) {

            $('.number_accessory_' + key).html('(x'+ value['count']+')').fadeIn(); // how many
            $('#holder_accessory_' + key).fadeIn();
            $('#subtotal_accessory_' + key).html(value['price'] * data['total_days']); // final price for accessory
        }
        else {
            $('.number_accessory_' + key).fadeOut().html('');
            $('#holder_accessory_' + key).fadeOut();
            $('#subtotal_accessory_' + key).html(value['price']);
        }
    }
}

function setModalMaxHeight(element) {
    this.$element     = $(element);
    this.$content     = this.$element.find('.modal-content');
    var borderWidth   = this.$content.outerHeight() - this.$content.innerHeight();
    var dialogMargin  = $(window).width() < 768 ? 20 : 60;
    var contentHeight = $(window).height() - (dialogMargin + borderWidth);
    var headerHeight  = this.$element.find('.modal-header').outerHeight() || 0;
    var footerHeight  = this.$element.find('.modal-footer').outerHeight() || 0;
    var maxHeight     = contentHeight - (headerHeight + footerHeight);

    this.$content.css({
        'overflow': 'hidden'
    });

    this.$element
        .find('.modal-body').css({
        'max-height': maxHeight,
        'overflow-y': 'auto'
    });
}



$(window).resize(function() {
    if ($('#policy_modal.in').length != 0) {
        setModalMaxHeight($('.modal.in'));
    }
});