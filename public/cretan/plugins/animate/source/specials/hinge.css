@keyframes hinge {
  0% {
    transform: rotate(0);
    transform-origin: top left;
    animation-timing-function: ease-in-out;
  }

  20%, 60% {
    transform: rotate(80deg);
    transform-origin: top left;
    animation-timing-function: ease-in-out;
  }

  40% {
    transform: rotate(60deg);
    transform-origin: top left;
    animation-timing-function: ease-in-out;
  }

  80% {
    transform: rotate(60deg) translateY(0);
    transform-origin: top left;
    animation-timing-function: ease-in-out;
    opacity: 1;
  }

  100% {
    transform: translateY(700px);
    opacity: 0;
  }
}

.hinge {
  animation-name: hinge;
}
