.header {
    .header-top {
        background-color: $yellow;
        padding: 0.5rem 0;

        .container {
            display: flex;
            align-items: center;
            justify-content: space-between;

            .header-contact {
                display: flex;
                gap: 1rem;

                img {
                    margin-right: 0.5rem;
                }
            }

            .header-info {
                flex-grow: 1;
                ul.tick-list {
                    list-style: none;
                    display: flex;
                    justify-content: center;
                    gap: 2rem;
                    li {
                        font-size: 14px;
                        font-weight: 400;
                        color: #000000;
                        position: relative;
                        margin: 0.5rem 0;
                        &:before {
                            content: "";
                            background-image: url(/images/icons/icon-tick-red.svg);
                            width: 20px;
                            height: 15px;
                            display: inline-block;
                            margin-right: 4px;
                            background-size: cover;
                            background-position: center;
                            background-repeat: no-repeat;
                        }
                    }
                }
            }
        }
    }

    .header-bottom {
        padding: .5rem 0;
        .container {
            display: flex;
            justify-content: space-between;
            align-items: center;

            .arrow {
                display: inline;
                svg {
                    width: 1rem;
                }
                &:hover {
                    path {
                        fill: $color-red;
                    }
                    
                }
            }

            nav.desktop-menu {
                display: flex;
                justify-content: space-around;
                flex-grow: 3;

                ul {
                    list-style: none;
                    width: 100%;
                    display: flex;
                    justify-content: center;
                    gap: 3rem;
                    // position: relative;

                    > li {
                        padding: 1rem;
                        font-weight: bold;
                        a {
                            cursor: pointer;
                            &:hover {
                                color: $color-red;
                            }
                        }  
                    }

                    li.current {
                        div {
                            a {
                                color: $red;
                            }
                        }
                    }

                    li.cars {          
                        
                        &:hover {
                            .arrow {
                                svg {
                                    path {
                                        fill: none;
                                        stroke: $color-red;
                                    }                                
                                }
                            }
                        }
                        
                        ul.opencars {
                            position: absolute;
                            left: 0;
                            width: 100%;
                            background: #fff;
                            z-index: 1111111;
                            margin-top: 1.6rem;
                            padding: 1.5rem 0 0.5rem;

                            li {
                                a {
                                    display: flex;
                                    flex-direction: column;
                                    justify-content: center;
                                    align-items: center;
                                    font-weight: normal;
                                    color: initial;
                                    svg {
                                        width: 3rem;
                                        height: 3rem;
                                    }

                                    &:hover {

                                        color: $color-red;
                                        svg path:first-child {
                                            fill: $red;
                                        }
                                        svg #Path_221,
                                        svg #Path_222,
                                        svg #Path_223,
                                        svg #Path_242,
                                        svg #Path_243,
                                        svg #Path_244,
                                        svg #Path_245,
                                        svg #Path_275,
                                        svg #Path_276,
                                        svg #Path_277,
                                        svg #Path_278,
                                        svg #Path_279 
                                        svg #Path_280,
                                        svg #Path_281,
                                        svg #Path_282,
                                        svg #Path_283,
                                        svg #Path_284,
                                        svg #Path_285,
                                        svg #Path_286,
                                        svg #Path_287,
                                        svg #Path_288,
                                        svg #Path_289,
                                        svg #Path_290,
                                        svg #Path_291,
                                        svg #Path_292,
                                        svg #Path_293,
                                        svg #Path_294,
                                        svg #Path_295,
                                        svg #Path_296,
                                        svg #Path_297 {
                                            fill: $red;
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }

            nav.main-menu {
                position: relative;
                flex-grow: 0;
                display: block;
                svg {
                    width: 2rem;
                }
                svg.icon {
                    width: 1rem;
                }
                ul {
                    display: flex;
                    flex-direction: column;
                    position: absolute;
                    right: 0;
                    z-index: 1000;
                    background-color: #fff;
                    width: 20rem;
                    text-align: right;
                    padding: 0.5rem;
                    list-style: none;
                    margin-top: 1.5rem;
                    border-radius: 8px;
                    li {
                        padding: 0.5rem;
                        margin-right: 0.5rem;
                        a {
                            cursor: pointer;
                        }

                        &:hover {
                            .arrow {
                                svg.icon {
                                    path {
                                        fill: $color-red;
                                    }                                    
                                }
                            }
                            
                        }

                        .submenu {
                            margin-top: 0;
                            li {
                                margin-right: 0.5rem;
                            }
                        }
                    }
                }
            }
        }
    }
}

@media only screen and (min-width: 768px) and (max-width: 860px) {
    .header {
        .header-bottom {
            .container {
                nav.desktop-menu {
                    display: none;
                }
            }
        }
    }
}

@media only screen and (min-width: 860px) and (max-width: 1200px) {
    .header {
        .header-bottom {
            .container {
                nav.desktop-menu {
                    ul {
                        gap: 2rem;
                    }
                }
            }
        }
    }
}


/* Max width 768px */

@media only screen and (max-width: 768px) {
    .header {
        .header-top {
            .container {
                flex-wrap: wrap;
                gap: 1rem;
                .header-contact {
                    flex: 60%;
                    order: 1;
                    #header-email {
                        display: none;
                    }
                    #header-phone {
                        >span {
                            display: none;
                        }
                    }
                }
                .header-info {
                    order: 3;
                    display: none;
                }
                .choose-language {
                    order: 2;
                }
            }            
        }
        .header-bottom {
            .container {
                nav.desktop-menu {
                    display: none;
                }
                nav.main-menu {
                    ul {
                        right: -1.3rem;
                        width: 100vW;
                        height: 100vH;
                        margin-top: 0;
                        .submenu {
                            margin-right: 1rem;
                        }
                    }
                }
            }
        }
    }

    
}

/* Width 560px - 1020px */

@media only screen and (min-width: 560px) and (max-width: 1020px) {
    
}

/* Width > 1020px */

@media only screen and (min-width: 1200px) {
   
    .header {
        .header-bottom {
            .container {
                nav.main-menu {
                    ul {
                        li.cars,
                        li.booking,
                        li.policy,
                        li.contact,
                        li.blog {
                            display: none;
                        }
                    }
                }
            }
        }
    }

}
