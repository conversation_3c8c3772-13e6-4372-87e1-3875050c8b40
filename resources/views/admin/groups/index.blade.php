@extends('layouts.admin.base')

@section('head_extra')
<link rel="stylesheet" type="text/css" href="{!! asset('packages/jquery-growl/jquery.growl.css') !!}">
@stop

@section('footer_js')
    <script src="{!! asset('packages/jquery-growl/jquery.growl.js') !!}"></script>
    <script src="{!! asset('js/jquery.admin-groups.js') !!}"></script>
@stop  

@section('body')

<section class=" padding stripes  ">
	<div class="container ">
		<div class="desktop-6   columns   "  >
			<h1 class="text bold condensed">Groups</h1>
		</div>
		<div class="desktop-6    columns right text   "  >
			 <a class="button" href="{!! route('admin.groups.create') !!}">Add new group</a>
		</div>
    </div>
</section>		
 
<section class="padding groupSort">
	{!! Form::open(array('route' => 'admin.sortOrder', 'class' => 'main')) !!}
		  @foreach ($groups as $group)
		  <div class="container groupSortItem">
		  <input type="hidden" name="groupID[]" value="{!! $group->id !!}" />
            <div class="bck white margin-bottom-small">
                <div class="desktop-1 columns   "  >
                	<div class="padding groupSortItemHandle">
                		<i class="fa fa-sort" aria-hidden="true"></i><span class="groupSortItemCount padding-left-tiny"></span>
                	</div>
    			</div>
                <div class="desktop-3 columns   "  >
    			<!-- START SEARCH ITEM -->
    				<div class="media">
    				  <div class="bd padding-right-large padding-bottom-small padding-left-small padding-small box_model"  >
    					<h2 class="text condensed  bold ">{!! $group->full_name !!}</h2>
    				  </div>
    				</div> 
    			</div><!-- END SEARCH ITEM -->
                <div class="desktop-3 columns   "  >
    			<!-- START SEARCH ITEM -->
    				  <div class="bd padding-right-large padding-bottom-small padding-left-small padding-small box_model"  >
    					<h4 class="text condensed">{!! $group->fuel_plan !== null ? 'Fuel plan: <strong>' . $group->fuel_plan . '&euro;</strong>' : '' !!}</h4>
    					<h4 class="text condensed">{!! $group->on_offer === 1 ? '<strong>On offer: ' . $group->offerTitle . '</strong>' : 'Regular prices' !!}</h4>
    				  </div>
    			</div><!-- END SEARCH ITEM -->
    			<div class="desktop-3 columns   "  >
    			<!-- START SEARCH ITEM -->
    				<div class="media">
    				  <div class="bd padding-right-large padding-bottom-small padding-left-small padding-small box_model" >
    				  @if (count($group->relatedGroups))
    				  <div class="text tiny bold margin-bottom-small">related groups</div>
    				  @endif
                          @foreach ($group->relatedGroups as $related)
                            <h4 class="text grey small">{!! $related->full_name !!} </h4>
                          @endforeach
    				  </div>
    				</div> 
    			</div><!-- END SEARCH ITEM -->
    		<div class="desktop-2 columns  text right padding-top-small"  >
    		{!! link_to_route('admin.groups.edit', 'Edit', $group->id, array('class'=>'button button-small')) !!}
    		</div>
    		<div class="clear"></div>
    		</div>
		</div>
		@endforeach	 
	{!! Form::close() !!}
</section>









 
@stop