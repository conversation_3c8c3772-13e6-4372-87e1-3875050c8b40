<div class="car-list-item" 
    data-supergroup="{{ $listing->group->supergroup?->id }}">

    <div class="booking-item">

        <div class="item-header">


            <div class="item-model">
                <h3>{!! $listing->shortTitle !!}</h3>
                <div x-data="{ showTip: false }" @mouseover="showTip = true" @mouseleave="showTip = false">
                    <span class="car-group">{{ trans('bookings.fleet.or_similar') }} Group {!! $listing->group->name !!}
                        {!! $listing->group->description !!}</span>
                    <span class="tooltip" x-show="showTip"
                        x-transition:enter.duration.500ms>{{ trans('bookings.fleet.or_similar_tooltip') }}</span>
                    <span class="info">i</span>
                </div>
            </div>

            <div class="attributes">
                <div class="attribute">
                    <img src="{!! asset('images/icons/passenger.svg') !!}" alt="passengers icon" />
                    <span>{!! $listing->seats !!}</span>
                </div>
                <div class="attribute">
                    <img src="{!! asset('images/icons/gear-shift.svg') !!}" alt="transmission icon" />
                    <span>{{ trans('common.' . $listing->transmission . '_transmission') }}</span>
                </div>
                <div class="attribute">
                    <img src="{!! asset('images/icons/door.svg') !!}" alt="doors icon" />
                    <span>{!! $listing->doors !!}</span>
                </div>
                @if ($listing->clima)
                    <div class="attribute">
                        <img src="{!! asset('images/icons/snowflake.svg') !!}" alt="clima icon" />
                        <span>{{ trans('listings.clima') }}</span>
                    </div>
                @endif
                @if ($listing->radio_cd)
                    <div class="attribute">
                        <img src="{!! asset('images/icons/music.svg') !!}" alt="radio-cd icon" />
                        <span>{{ trans('listings.radio_cd') }}</span>
                    </div>
                @endif
            </div>

            <div class="item-price">
                @if ($date_range->getTotalDays() <= 2 && $date_range->getTotalDays() > 0)
                    <div class="car-price-from"> {{ trans('listings.from') }} </div>
                    <div class="car-price-container">

                        <span class="car-price">{!! $listing->group->base_price !!}<sup>€</sup></span>
                        <span class="car-period">
                            {!! trans('listings.for') !!}
                            {!! $date_range->getTotalDays() !!} {!! trans('reservation.days') !!}
                        </span>
                    </div>
                @elseif($date_range->getTotalDays() > 2)
                    @if ($offer->hasAvailability())
                        <div class="car-price-from"> {{ trans('listings.from') }} </div>
                        <div class="car-price-container">

                            <span class="car-price">{!! $offer->getTotalDiscountPrice() !!}€ </span>
                            <span class="car-period">
                                {!! trans('listings.for') !!}
                                {!! $date_range->getTotalDays() !!} {!! trans('reservation.days') !!}
                            </span>
                        </div>
                    @endif
                @endif
            </div>
        </div>

        <div class="item-details">

            <div class="main-photo">


                <?php $main_photo = $listing->getFirstMedia('photos'); ?>
                @if (!empty($main_photo))
                    <?php $main_photo_attributes = $main_photo->getSeoAttributes(); ?>
                    {{ $main_photo->img()->attributes($main_photo_attributes) }}
                @else
                    <img src="{{ asset('/images/nophoto.png') }}" alt="{{ $listing->model }}">
                @endif

                <div class="badges">
                    @if ($listing->group->on_offer)
                        <div class="badge-item offer">
                            <img src="{!! asset('images/icons/hot-offer.svg') !!}" alt="on offer badge" /> <span>HOT OFFER</span>
                        </div>
                    @endif
                    @if ($listing->popular)
                        <div class="badge-item popular">
                            <img src="{!! asset('images/icons/best-seller.svg') !!}" alt="popular badge" /> <span> BEST SELLER</span>
                        </div>
                    @endif

                </div>

            </div>

            <div class="item-info" x-data="{ show: false }">

                <ul>
                    <?php $visible_features = trans('bookings.fleet.listing_features_visible'); ?>
                    @foreach ($visible_features as $feature)
                        <li x-data="{ showTip: false }" @mouseover="showTip = true" @mouseleave="showTip = false"
                            class="{{ $feature['class'] }}">
                            {{ $feature['text'] }}
                            <span class="tooltip" x-show="showTip"
                                x-transition:enter.duration.500ms>{{ $feature['tooltip'] }}</span>
                        </li>
                    @endforeach

                    <li class="action" x-show="!show" @click="show = true">{{ trans('common.show_more') }}</li>

                    <div x-show="show" x-transition.duration.500ms>
                        <?php $invisible_features = trans('bookings.fleet.listing_features_invisible'); ?>
                        @foreach ($invisible_features as $feature)
                            <li x-data="{ showTip: false }" @mouseover="showTip = true" @mouseleave="showTip = false"
                                class="{{ $feature['class'] }}">
                                {{ $feature['text'] }}
                                <span class="tooltip" x-show="showTip"
                                    x-transition:enter.duration.500ms>{{ $feature['tooltip'] }}</span>
                            </li>
                        @endforeach
                        {{--                        <li x-data="{showTip: false}" @mouseover="showTip = true" @mouseleave="showTip = false"> --}}
                        {{--                            Unlimited mileage --}}
                        {{--                            <span class="tooltip" x-show="showTip" x-transition:enter.duration.500ms>Lorem ipsum dolor sit amet.</span> --}}
                        {{--                        </li> --}}
                        {{--                        <li x-data="{showTip: false}" @mouseover="showTip = true" @mouseleave="showTip = false"> --}}
                        {{--                            All taxes included --}}
                        {{--                            <span class="tooltip" x-show="showTip" x-transition:enter.duration.500ms>Lorem ipsum dolor sit amet.</span> --}}
                        {{--                        </li> --}}
                        {{--                        <li x-data="{showTip: false}" @mouseover="showTip = true" @mouseleave="showTip = false"> --}}
                        {{--                            Additional driver option --}}
                        {{--                            <span class="tooltip" x-show="showTip" x-transition:enter.duration.500ms>Lorem ipsum dolor sit amet.</span> --}}
                        {{--                        </li> --}}
                        {{--                        <li x-data="{showTip: false}" @mouseover="showTip = true" @mouseleave="showTip = false"> --}}
                        {{--                            Child seats available --}}
                        {{--                            <span class="tooltip" x-show="showTip" x-transition:enter.duration.500ms>Lorem ipsum dolor sit amet.</span> --}}
                        {{--                        </li> --}}
                        {{--                        <li x-data="{showTip: false}" @mouseover="showTip = true" @mouseleave="showTip = false"> --}}
                        {{--                            24/7 call center --}}
                        {{--                            <span class="tooltip" x-show="showTip" x-transition:enter.duration.500ms>Lorem ipsum dolor sit amet.</span> --}}
                        {{--                        </li> --}}
                    </div>
                    <li class="action" x-show="show" @click="show = false">{{ trans('common.show_less') }}</li>
                </ul>

            </div>
            <div class="cta">

                <div class="cta-button">
                    <div>
                        @if ($offer->hasAvailability() && $date_range->getTotalDays() > 0)
                            {!! link_to_route(
                                'listings.show',
                                trans('forms.book_now'),
                                [$listing->slug, 'ref_' => 'listings_index'],
                                ['class' => 'btn btn-yellow'],
                            ) !!}
                        @else
                            {!! link_to_route(
                                'listings.show',
                                trans('listings.check_availability'),
                                [$listing->slug, 'ref_' => 'listings_index'],
                                ['class' => 'btn btn-grey'],
                            ) !!}
                        @endif
                    </div>

                    <div class="item-extra-bottom">
                        <ul>
                            <li>{!! trans('listings.price_is_final') !!}</li>
                            @if ($listing->group->name != 'G1')
                                <li>{!! trans('listings.no_card') !!}</li>
                                <li>{!! trans('listings.all_inclusive') !!}</li>
                            @else
                                <li>{!! trans('listings.coverage_with_excess') !!}</li>
                            @endif
                        </ul>

                    </div>
                </div>
            </div>
        </div>
    </div>
</div>


