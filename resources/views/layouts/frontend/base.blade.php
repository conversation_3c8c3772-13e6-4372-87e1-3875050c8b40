<!doctype html>
<html class="no-js" lang="{{ LaravelLocalization::getCurrentLocale() }}">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <link rel="shortcut icon" type="image/x-icon" href="{!! asset('images/favicon.ico') !!}">

    <title>
        @section('title') {!! Lang::get('common.default_title') !!}@show<?php /* {!! Lang::get('common.seo_site_name') !!} */ ?>
        </title>

        <meta name="description"
            content="@section('meta_description'){!! Lang::get('common.default_meta_description') !!}@show">

    @section('head')

    <!-- Google fonts go here -->
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
        <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" />
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.2.1/css/all.min.css" integrity="sha512-MV7K8+y+gLIBoVD59lQIYicR65iaqukzvf/nwasF0nqhPay5w/9lJmVM2hMDcnK1OnMGCdVK+iQrJ7lzPJQd1w==" crossorigin="anonymous" referrerpolicy="no-referrer" />
        <link rel="stylesheet" type="text/css" href="{{ asset('css/vendor.css') }}?v={!! config('app.styles_version') !!}">
        {{-- <link rel="stylesheet" type="text/css" href="{!! asset('css/bundle.css') !!}?v={!! config('app.styles_version'); !!}">
        <link rel="stylesheet" type="text/css" href="{!! asset('css/extend.css') !!}?v={!! config('app.styles_version'); !!}"> --}}
        @livewireStyles
        <link rel="stylesheet" type="text/css" href="{{ mix('css/app.css') }}">
        <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css">
        <link rel="stylesheet" type="text/css" href="https://npmcdn.com/flatpickr/dist/themes/material_red.css">
        <script async 
                src="https://maps.googleapis.com/maps/api/js?key={{ config('services.google_maps.api_key') }}"></script>

        {{-- Google Rcaptcha --}}
        <script src="https://www.google.com/recaptcha/api.js"></script>

        <meta name="theme-color" content="#fafafa" />

        @if (!empty(Route::getFacadeRoot()->current()) && isset($enabledLanguages))
                <?php
                $routes = trans('routes');
                $route = array_search(str_replace(LaravelLocalization::getCurrentLocale() . '/', '', Route::getFacadeRoot()->current()->uri()), $routes);
                ?>

            @if (Route::currentRouteName() === 'posts.show')
                @php($currentLocale = App::getLocale())
                @foreach ($enabledLanguages as $enabledLanguage)
                    @if ($post->hasTranslation($enabledLanguage->locale))
                        @php(App::setLocale($enabledLanguage->locale))
                        <link rel="alternate" href="{{ LaravelLocalization::localizeUrl(route('posts.show', $post->slug), $enabledLanguage->locale) }}{!! $url_variables !!}" hreflang="{!! $enabledLanguage->locale !!}">
                    @endif
                @endforeach
                @php(App::setLocale($currentLocale))
            @else
                @foreach ($enabledLanguages as $enabledLanguage)
                    <?php $link = empty($route) ? LaravelLocalization::getLocalizedURL($enabledLanguage->locale) : LaravelLocalization::getURLFromRouteNameTranslated($enabledLanguage->locale, "routes.{$route}", array_filter(Route::getCurrentRoute()->parameters())); ?>
                    <link rel="alternate" href="{!! $link !!}{!! $url_variables !!}" hreflang="{!! $enabledLanguage->locale !!}">
                @endforeach
            @endif
        @endif

        @yield('head_extra')
        @show

        @if (App::environment('production'))
        <!-- Google tag (gtag.js) -->
        <script async src="https://www.googletagmanager.com/gtag/js?id=G-CY9VCX8XNY"></script>
        <script>
            window.dataLayer = window.dataLayer || [];
            function gtag(){dataLayer.push(arguments);}
            gtag('js', new Date());

            gtag('config', 'G-CY9VCX8XNY');
        </script>



                <!-- Begin Google Analytics Code -->
        <script>
            (function (i, s, o, g, r, a, m) {
                i['GoogleAnalyticsObject'] = r;
                i[r] = i[r] || function () {
                            (i[r].q = i[r].q || []).push(arguments)
                        }, i[r].l = 1 * new Date();
                a = s.createElement(o),
                        m = s.getElementsByTagName(o)[0];
                a.async = 1;
                a.src = g;
                m.parentNode.insertBefore(a, m)
            })(window, document, 'script', '//www.google-analytics.com/analytics.js', 'ga');

            ga('create', 'UA-53751373-1', 'auto');
            ga('send', 'pageview');

        </script>

        <!-- Begin Cookie Consent plugin by Silktide - http://silktide.com/cookieconsent -->
        <script type="text/javascript">
            window.cookieconsent_options = {
                "message": "Eurodollar uses cookies to ensure you get the best browsing experience and to help us improve the site",
                "dismiss": "Got it!",
                "learnMore": "More info",
                "link": null,
                "theme": "dark-bottom"
            };
        </script>

        <script type="text/javascript"
                src="//cdnjs.cloudflare.com/ajax/libs/cookieconsent2/1.0.9/cookieconsent.min.js"></script>
        <!-- End Cookie Consent plugin -->

        @endif
</head>
<body
    class="ll-skin-nigran
    {{ request()->routeIs('posts.index') || request()->routeIs('post.tags.index') || request()->routeIs('search.index')  ? 'blog' : '' }}
    {{ request()->routeIs('posts.show') ? 'blog-post' : '' }}
    {{ request()->routeIs('booking.show') ? 'booking-page' : '' }}
    {{ request()->routeIs('listings.index') ? 'fleet-page' : '' }}
    {{ request()->routeIs('booking.fleet.index') ? 'booking-fleet-page' : '' }}"
>
@include('layouts.frontend.header')

<div class="site-content">
@yield('body')
</div>

@include('layouts.frontend.footer')

<script src="{!! asset('js/vendor.js') !!}?v={!! config('app.scripts_version') !!}"></script>

<script type="module" src="{!! asset('js/app.js') !!}?v={!! config('app.scripts_version') !!}"></script>

@if (isset($current_locale) && $current_locale !== 'en')
{{-- <script src="{!! asset('packages/datepicker/i18n/datepicker-'.$current_locale.'.js') !!}"></script> --}}


@endif

    @livewireScripts
    <script src="{!! asset('packages/js.cookie.js') !!}?v={!! config('app.scripts_version') !!}"></script>
    
@section('footer_js')
<script>
    function onSubmit(token) {
        
        document.getElementById("contactForm").submit();
    }
</script>

@yield('footer_js')
<div class="hidden"
            id="current_locale" title="{!! isset($current_locale) ? $current_locale : '' !!}">
        </div>
        <script src="{{ mix('js/rental-app.js') }}"></script>

        </body>

    </html>
