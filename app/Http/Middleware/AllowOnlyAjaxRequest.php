<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Request;

class AllowOnlyAjaxRequest {

    /**
     * Returns a 403 http error for all requests that are not AJAX
     *
     * @param  \Illuminate\Http\Request $request
     * @param  \Closure $next
     * @return mixed
     */
    public function handle($request, Closure $next)
    {
        if (!Request::ajax())
        {
            App::abort(403, 'Unauthorized action.');
        }

        return $next($request);
    }
}
