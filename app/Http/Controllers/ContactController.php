<?php

namespace App\Http\Controllers;

use App\ContactLead;
use App\Newsletter;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Spatie\SchemaOrg\Schema;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\URL;
use Illuminate\Support\Facades\Lang;
use Illuminate\Support\Facades\Mail;
use App\Http\Requests\ContactRequest;
use App\Rules\InvisibleRecaptchaRule;
use Illuminate\Support\Facades\Redirect;
use Illuminate\Support\Facades\Response;
use Illuminate\Support\Facades\Validator;

class ContactController extends Controller
{

    /**
     * Show the contact page
     *
     * @return mixed
     */
    public function showContact()
    {
        $this->view_data['contact_selected'] = 'current';

        // seo stuff
        // canonical url
        $this->view_data['canonical_url'] = \Illuminate\Support\Facades\Request::url();

        // json-ld microdata script (echoed in view)
        $this->view_data['breadcrumbs_schema'] = Schema::breadcrumbList()
            ->itemListElement(
                array(
                    Schema::listItem()
                        ->position(1)
                        ->item(
                            array(
                                '@id' => route('home'),
                                'name' => trans('breadcrumbs.home'),
                            )
                        ),
                    Schema::listItem()
                        ->position(2)
                        ->item(
                            array(
                                '@id' => $this->view_data['canonical_url'],
                                'name' => trans('breadcrumbs.contact'),
                            )
                        ),
                )
            );

        return Response::view('frontend.contact', $this->view_data);
    }

    /**
     * Handles the submission of the contact form
     *
     * @return mixed
     */
    public function handleContact(ContactRequest $request)
    {
        // validation rules
        $validation_rules = [
            'name' => ['required'],
            'email' => ['required', 'email'],
            //			'phone' => ['required'],
            'comment' => ['required'],
            'acceptTermsCheckbox' => ['required'],
            'g-recaptcha-response' => [
                'required',
                new InvisibleRecaptchaRule
            ],
        ];

        $input = $request->all();

        $validator = Validator::make($input, $validation_rules);

        if ($validator->fails()) {
            return Redirect::to(URL::previous() . "#contact-form")->withInput()->withErrors($validator);
        }

        // initialize spam flag
        $spam = false;
        // we do not want to process submissions with urls in the message
        if (preg_match_all('#\bhttps?://[^\s()<>]+(?:\([\w\d]+\)|([^[:punct:]\s]|/))#', $request->input('comment')))
        {
            $spam = true;
        }

        // persist contact lead to DB
        $contact_lead = new ContactLead([
            'name'      => $input['name'],
            'email'     => $input['email'],
            'telephone' => $input['phone'],
            'comment'   => $input['comment'],
            'origin'    => $input['origin'],
            'site'      => 1,
        ]);

        if($spam)
        {
            $contact_lead->intercepted = true;
        }

        $contact_lead->save();

        // send the email only on the condition that it is not spam
        if( ! $spam )
        {
            // Change local to en
            $currentLocale = App::getLocale();
            App::setLocale('en');

            // send email
            Mail::send('emails.contact', $input, function ($message) {
                $message->to(config('mail_addresses.argiry'), 'Argiry Fragkaki')
                    ->cc([config('mail_addresses.kiritsis'), '<EMAIL>'])
                    ->subject('Rentcars-crete contact form ' . date("d/m/Y H:i"));
            });

            Mail::send('emails.contact', $input, function ($message) {
                $message->to(config('mail_addresses.info_ed'), 'RentCars-Crete')
                    ->subject('Rentcars-crete contact form ' . date("d/m/Y H:i"));
            });
            // Reset local
            App::setLocale($currentLocale);
        }

        return Redirect::to(URL::previous() . "#contact-form")->with(['success' => Lang::get('contact.success')]);
    }

    /**
     * Handles the submission of the newsletter form
     *
     * @return mixed
     */
    public function handleNewsletter(Request $request)
    {
        // validation rules
        $validation_rules = [
            'newsletter_email' => ['required', 'email'],
        ];

        $input = $request->all();

        $validator = Validator::make($input, $validation_rules);

        if ($validator->fails()) {
            $messages = $validator->messages();
            return Response::json(['status' => 'error', 'msg' => $messages]);
        }

        // save the email
        Newsletter::updateOrCreate(['newsletter_email' => $input['newsletter_email']], $input);

        return Response::json(['status' => 'success', 'msg' => 'Email saved thank you']);
    }

    /**
     * Show the page to present the newsletter list
     */
    public function getNewslettersIndex()
    {
        $this->view_data['addresses'] = Newsletter::orderBy('id', 'DESC')->get();

        $this->view_data['newsletters_selected'] = 'current';

        return Response::view('admin.newsletters.index', $this->view_data);
    }
}
