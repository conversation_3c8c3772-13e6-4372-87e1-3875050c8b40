<?php namespace App\Http\Controllers;

use Illuminate\Support\Facades\Request;

class ReviewsController extends Controller {


    public function __construct()
    {
        parent::__construct();

        // canonical url initialization
        $this->view_data['canonical_url'] = Request::url();
    }

    public function index()
    {
        // seo
        $this->view_data['page_title']          = trans('reviews.index.page_title');
        $this->view_data['page_description']    = trans('reviews.index.page_description');

        // json-ld microdata script (echoed in view)
//        $this->view_data['local_business_schema'] = Schema::localBusiness()
//            ->url(route('home'))
//            ->logo(asset('images/logo.svg'))
//            ->image(asset('images/logo.svg'))
//            ->name(trans('common.site_name'))
//            ->email(config('schema_org.local_business.email_address'))
//            ->priceRange(config('schema_org.local_business.price_range'))
//            ->hasMap(config('schema_org.local_business.google_maps_place'))
//            ->address(Schema::postalAddress()
//                ->addressCountry(config('schema_org.local_business.address.country'))
//                ->addressRegion(config('schema_org.local_business.address.region'))
//                ->addressLocality(config('schema_org.local_business.address.locality'))
//                ->postalCode(config('schema_org.local_business.address.postal_code'))
//                ->streetAddress(config('schema_org.local_business.address.street_address'))
//            )
//            ->telephone(trans('common.phone'))
//            ->openingHours(config('schema_org.local_business.opening_hours'))
//            ->aggregateRating(Schema::aggregateRating()
//                ->bestRating(config('schema_org.local_business.aggregate_rating.best'))
//                ->worstRating(config('schema_org.local_business.aggregate_rating.worst'))
//                ->ratingCount(config('schema_org.local_business.aggregate_rating.rating_count'))
//                ->ratingValue(config('schema_org.local_business.aggregate_rating.rating_value'))
//            );


        // json-ld microdata script (echoed in view)
//        $this->view_data['product_schema'] = Schema::product()
//            ->name(trans('common.product_name.agios_nikolaos'))
//            ->aggregateRating(Schema::aggregateRating()
//                ->bestRating(config('schema_org.local_business.aggregate_rating.best'))
//                ->worstRating(config('schema_org.local_business.aggregate_rating.worst'))
//                ->ratingCount(config('schema_org.local_business.aggregate_rating.agios_nikolaos.rating_count'))
//                ->ratingValue(config('schema_org.local_business.aggregate_rating.agios_nikolaos.rating_value'))
//            );

        return view('frontend.reviews.index', $this->view_data);
        // return view('frontend.reviews.index');
    }

}
