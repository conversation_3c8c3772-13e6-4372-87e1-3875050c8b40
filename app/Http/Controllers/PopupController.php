<?php

namespace App\Http\Controllers;

use App\Popup;
use Illuminate\Http\Request;
use App\Services\Validation\ValidationException;
use Illuminate\Support\Facades\Redirect;
use Illuminate\Support\Facades\View;

class PopupController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     *
     */
    public function index()
    {
        $this->view_data['content_group_selected'] = 'current';
        $this->view_data['popup_selected'] = 'current';

        $this->view_data['popup'] = Popup::first();

        return View::make('admin.popup.index', $this->view_data);
    }

    /**
     * Show the form for creating a new resource.
     *
     *
     */
    public function create()
    {
        $this->view_data['add_popup_selected'] = 'current';
        $this->view_data['upload_max_filesize'] = $this->return_bytes(ini_get('upload_max_filesize'));
        $this->view_data['upload_max_filesize_short'] = ini_get('upload_max_filesize');

        return View::make('admin.popup.create', $this->view_data);
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     *
     */
    public function store(Request $request)
    {
        try
        {
            $popup = Popup::create($request->all());

            $popup->save();

            // return to the coupons list
            return Redirect::route('admin.popup.index', $popup->id)->with('success', 'Popup created');
        }
        catch(ValidationException $e)
        {
            return Redirect::back()->withInput()->withErrors($e->getErrors());
        }
    }


    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     *
     */
    public function edit($id)
    {
        $popup = Popup::findOrFail($id);
        $this->view_data['content_group_selected'] = 'current';
        $this->view_data['popup_selected'] = 'current';
        $this->view_data['upload_max_filesize'] = $this->return_bytes(ini_get('upload_max_filesize'));
        $this->view_data['upload_max_filesize_short'] = ini_get('upload_max_filesize');

        return View::make('admin.popup.edit', $this->view_data)->with('popup', $popup);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     *
     */
    public function update(Request $request, $id)
    {
        // check the existence of the given id
        $popup = Popup::findOrFail($id);

        try
        {
            $popup->validateAndUpdate($request->all());

            // return to the edit coupon screen
            return Redirect::route('admin.popup.edit', $popup->id)->with('success', 'Popup saved');
        }
        catch(ValidationException $e)
        {
            return Redirect::back()->withInput()->withErrors($e->getErrors());
        }
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     *
     */
    public function destroy($id)
    {
        // check the existence of the given id
        $popup = Popup::findOrFail($id);

        // proceed with deleting the post row
        $popup->delete();

        // return to the posts list screen
        return Redirect::route('admin.popup.index')->with('success', 'Popup deleted');
    }

    /**
     * Convert byte value from shorthand byte notation
     * @param string $val
     */
    private function return_bytes($val) {
        $val = trim($val);
        $last = strtolower($val[strlen($val)-1]);
        $val = (int)$val;
        switch($last) {
            // The 'G' modifier is available since PHP 5.1.0
            case 'g':
                $val *= 1024;
            case 'm':
                $val *= 1024;
            case 'k':
                $val *= 1024;
        }

        return $val;
    }
}
