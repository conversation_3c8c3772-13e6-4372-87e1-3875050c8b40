<?php

namespace App\Services\Search\Filters;

use Illuminate\Database\Eloquent\Builder;

class ReviewRating extends GlobalFilter
{
    /**
     * Filter key to use in query
     *
     * @var string
     */
    protected static string $filter_key = 'review_rating';

    /**
     * Apply a given search value to the builder instance.
     *
     * @param Builder $builder
     * @param mixed $value
     * @return Builder $builder
     */
    public static function apply(Builder $builder, $value): Builder
    {
        return self::filterEqual($builder, $value);
    }
}