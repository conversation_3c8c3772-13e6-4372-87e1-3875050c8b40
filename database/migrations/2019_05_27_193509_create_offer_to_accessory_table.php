<?php

use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateOfferToAccessoryTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('offer_to_accessory', function($table)
        {
            $table->increments('id');
            $table->integer('offer_id');
            $table->integer('accessory_id');
            $table->integer('accessory_amount');
            $table->integer('accessory_price');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::drop('offer_to_accessory');
    }
}
