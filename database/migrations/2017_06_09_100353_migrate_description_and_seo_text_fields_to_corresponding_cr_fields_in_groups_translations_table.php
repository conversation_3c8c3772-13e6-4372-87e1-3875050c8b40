<?php

use App\Language;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\Config;

use App\Group;

class MigrateDescriptionAndSeoTextFieldsToCorrespondingCrFieldsInGroupsTranslationsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {

        $groups = Group::all();
        Config::set('translationLocales', Language::all()->pluck('locale', 'id'));

        foreach ($groups as $group)
        {
//            echo 'ok';
            $origLocale = App::getLocale();

            foreach (Config::get('translationLocales') as $locale)
            {
                App::setLocale($locale);

                $group->translateOrNew($locale)->description_cr = $group->description;
                $group->translateOrNew($locale)->seo_text_cr = $group->seo_text;
            }

            App::setLocale($origLocale);

            $group->save();
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        //
    }
}
